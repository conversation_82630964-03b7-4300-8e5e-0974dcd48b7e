"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, BookOpen, Settings, Bookmark, Search, Highlighter, MessageCircle, X, Palette, Type } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useNavigation } from "@/contexts/NavigationContext"

interface Chapter {
  id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
}

interface Highlight {
  id: string
  start_position: number
  end_position: number
  selected_text: string
  color: string
  is_private: boolean
  user_id: string
  created_at: string
}

interface MarginComment {
  id: string
  content: string
  position_data: {
    startPos: number
    endPos: number
    anchorText: string
  }
  user_id: string
  parent_id?: string
  created_at: string
  replies?: MarginComment[]
}

interface EbookReaderProps {
  chapters?: Chapter[]
  bookTitle: string
  authorName: string
  projectId: string
  userId?: string
  onClose: () => void
  isPreview?: boolean
  onApprovePreview?: () => void
  pdfUrl?: string
}

export function EbookReader({ chapters = [], bookTitle, authorName, projectId, userId, onClose, isPreview = false, onApprovePreview, pdfUrl }: EbookReaderProps) {
  const supabase = createSupabaseClient()
  const { hideNavigation, showNavigation } = useNavigation()

  // Core reader state
  const [currentChapter, setCurrentChapter] = useState(0)
  const [fontSize, setFontSize] = useState(16)
  const [fontFamily, setFontFamily] = useState('serif')
  const [lineHeight, setLineHeight] = useState(1.6)
  const [theme, setTheme] = useState('light')
  const [showSettings, setShowSettings] = useState(false)
  const [showChapterList, setShowChapterList] = useState(false)
  const [showFontSizeMenu, setShowFontSizeMenu] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [bookmarks, setBookmarks] = useState<number[]>([])

  // Social features state
  const [highlights, setHighlights] = useState<Highlight[]>([])
  const [marginComments, setMarginComments] = useState<MarginComment[]>([])
  const [selectedText, setSelectedText] = useState<{
    text: string
    startPos: number
    endPos: number
  } | null>(null)
  const [showHighlightMenu, setShowHighlightMenu] = useState(false)
  const [showCommentForm, setShowCommentForm] = useState(false)
  const [highlightColor, setHighlightColor] = useState('#ffeb3b')
  const [newComment, setNewComment] = useState('')
  const [isPageTurning, setIsPageTurning] = useState(false)

  // Refs for text selection and page animation
  const contentRef = useRef<HTMLDivElement>(null)
  const pageRef = useRef<HTMLDivElement>(null)

  // Hide navigation when reader opens, show when it closes
  useEffect(() => {
    hideNavigation()
    return () => showNavigation()
  }, [hideNavigation, showNavigation])

  // Wrapper function to ensure navigation is shown when closing
  const handleClose = useCallback(() => {
    showNavigation()
    onClose()
  }, [showNavigation, onClose])

  // Calculate reading progress
  useEffect(() => {
    const totalChapters = chapters.length
    const progress = totalChapters > 0 ? ((currentChapter + 1) / totalChapters) * 100 : 0
    setReadingProgress(progress)
  }, [currentChapter, chapters.length])

  // Load highlights and comments for current chapter
  useEffect(() => {
    if (!userId || !chapters[currentChapter]) return

    const loadChapterData = async () => {
      const chapterId = chapters[currentChapter].id

      // Load highlights
      const { data: highlightsData } = await supabase
        .from('highlights')
        .select('*')
        .eq('chapter_id', chapterId)
        .eq('project_id', projectId)

      // Load margin comments
      const { data: commentsData } = await supabase
        .from('margin_comments')
        .select('*')
        .eq('chapter_id', chapterId)
        .eq('project_id', projectId)
        .order('created_at', { ascending: true })

      setHighlights(highlightsData || [])
      setMarginComments(commentsData || [])
    }

    loadChapterData()
  }, [currentChapter, projectId, userId, supabase, chapters])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.font-size-dropdown')) {
        setShowFontSizeMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Enhanced page turning with animation and auto-scroll to top
  const nextChapter = useCallback(() => {
    if (currentChapter < chapters.length - 1 && !isPageTurning) {
      setIsPageTurning(true)
      setTimeout(() => {
        setCurrentChapter(currentChapter + 1)
        setIsPageTurning(false)
        // Scroll to top of the new chapter
        if (pageRef.current) {
          pageRef.current.scrollTo({ top: 0, behavior: 'smooth' })
        }
      }, 300)
    }
  }, [currentChapter, chapters.length, isPageTurning])

  const prevChapter = useCallback(() => {
    if (currentChapter > 0 && !isPageTurning) {
      setIsPageTurning(true)
      setTimeout(() => {
        setCurrentChapter(currentChapter - 1)
        setIsPageTurning(false)
        // Scroll to top of the new chapter
        if (pageRef.current) {
          pageRef.current.scrollTo({ top: 0, behavior: 'smooth' })
        }
      }, 300)
    }
  }, [currentChapter, isPageTurning])

  // Text selection handling
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return

    const range = selection.getRangeAt(0)
    const selectedText = selection.toString().trim()

    if (selectedText.length > 0 && contentRef.current?.contains(range.commonAncestorContainer)) {
      const startPos = range.startOffset
      const endPos = range.endOffset

      setSelectedText({
        text: selectedText,
        startPos,
        endPos
      })
      setShowHighlightMenu(true)
    }
  }, [])

  // Create highlight
  const createHighlight = async (color: string, isPrivate: boolean = false) => {
    if (!selectedText || !userId || !chapters[currentChapter]) return

    const chapterId = chapters[currentChapter].id

    const { data, error } = await supabase
      .from('highlights')
      .insert({
        user_id: userId,
        project_id: projectId,
        chapter_id: chapterId,
        start_position: selectedText.startPos,
        end_position: selectedText.endPos,
        selected_text: selectedText.text,
        color,
        is_private: isPrivate
      })
      .select()
      .single()

    if (!error && data) {
      setHighlights(prev => [...prev, data])

      // Update passage popularity
      await supabase.rpc('increment_passage_popularity', {
        p_project_id: projectId,
        p_chapter_id: chapterId,
        p_passage_text: selectedText.text
      })
    }

    setSelectedText(null)
    setShowHighlightMenu(false)
    window.getSelection()?.removeAllRanges()
  }

  // Create margin comment
  const createMarginComment = async () => {
    if (!selectedText || !newComment.trim() || !userId || !chapters[currentChapter]) return

    const chapterId = chapters[currentChapter].id

    const { data, error } = await supabase
      .from('margin_comments')
      .insert({
        user_id: userId,
        project_id: projectId,
        chapter_id: chapterId,
        position_data: {
          startPos: selectedText.startPos,
          endPos: selectedText.endPos,
          anchorText: selectedText.text
        },
        content: newComment.trim()
      })
      .select()
      .single()

    if (!error && data) {
      setMarginComments(prev => [...prev, data])
    }

    setNewComment('')
    setShowCommentForm(false)
    setSelectedText(null)
    window.getSelection()?.removeAllRanges()
  }

  const toggleBookmark = () => {
    if (bookmarks.includes(currentChapter)) {
      setBookmarks(bookmarks.filter(b => b !== currentChapter))
    } else {
      setBookmarks([...bookmarks, currentChapter])
    }
  }

  const getThemeClasses = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gray-900 text-gray-100'
      case 'sepia':
        return 'bg-yellow-50 text-yellow-900'
      default:
        return 'bg-white text-gray-900'
    }
  }

  // Render highlights and formatting in text
  const renderTextWithHighlights = (text: string) => {
    // First apply formatting
    let result = text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')

    // Then apply highlights
    if (highlights.length > 0) {
      highlights.forEach(highlight => {
        const highlightedText = `<mark style="background-color: ${highlight.color}; padding: 2px 4px; border-radius: 3px;" data-highlight-id="${highlight.id}">${highlight.selected_text}</mark>`
        result = result.replace(highlight.selected_text, highlightedText)
      })
    }

    return result
  }

  const currentChapterData = chapters[currentChapter]
  const isPdf = !!pdfUrl

  if (!isPdf && !currentChapterData) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <div className="text-red-500 text-4xl mb-4">📚</div>
            <h3 className="text-lg font-semibold mb-2">No Chapters Available</h3>
            <p className="text-gray-600 mb-4">This book doesn't have any readable chapters yet.</p>
            <Button onClick={handleClose}>Close Reader</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const themeClasses = getThemeClasses()

  return (
    <div className={`fixed inset-0 z-50 ${themeClasses}`}>
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        {/* Mobile Header - Stacked Layout */}
        <div className="block sm:hidden">
          <div
            className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            onClick={handleClose}
            title="Click to close reader"
          >
            <div className="flex-1 min-w-0">
              <h1
                className="font-semibold text-base"
                style={{
                  maxWidth: '180px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                title={bookTitle}
              >
                {bookTitle}
              </h1>
              <p className="text-sm opacity-70 truncate">by {authorName}</p>
            </div>
            <div
              className="flex items-center space-x-1"
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.location.href = '/library'}
                title="Go to Library"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleBookmark}
                className={bookmarks.includes(currentChapter) ? 'text-yellow-500' : ''}
                title="Bookmark Chapter"
              >
                <Bookmark className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHighlightMenu(!showHighlightMenu)}
                className="relative"
                title="Highlights"
              >
                <Highlighter className="h-4 w-4" />
                {highlights.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    {highlights.length}
                  </span>
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCommentForm(!showCommentForm)}
                className="relative"
                title="Comments"
              >
                <MessageCircle className="h-4 w-4" />
                {marginComments.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    {marginComments.length}
                  </span>
                )}
              </Button>
              {/* Font Size Dropdown */}
              <div className="relative font-size-dropdown">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFontSizeMenu(!showFontSizeMenu)}
                  title="Font Size"
                >
                  <Type className="h-4 w-4" />
                </Button>
                {showFontSizeMenu && (
                  <div className="absolute bottom-full right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 z-50">
                    <div className="flex flex-col space-y-2 min-w-[140px]">
                      <div className="text-xs font-medium text-center text-gray-600 dark:text-gray-400">Font Size</div>
                      <input
                        type="range"
                        min="12"
                        max="24"
                        step="2"
                        value={fontSize}
                        onChange={(e) => setFontSize(parseInt(e.target.value))}
                        className="w-full"
                      />
                      <div className="text-xs text-center font-medium">{fontSize}px</div>
                    </div>
                  </div>
                )}
              </div>
              <Button variant="ghost" size="sm" onClick={() => setShowChapterList(!showChapterList)} title="Chapter List">
                <BookOpen className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)} title="Reader Settings">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Desktop Header - Horizontal Layout */}
        <div className="hidden sm:flex items-center justify-between p-4">
          <div
            className="flex items-center space-x-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors rounded-lg p-2 -m-2"
            onClick={handleClose}
            title="Click to close reader"
          >
            <div>
              <h1
                className="font-semibold text-lg"
                style={{
                  maxWidth: '300px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                title={bookTitle}
              >
                {bookTitle}
              </h1>
              <p className="text-sm opacity-70">by {authorName}</p>
            </div>
          </div>

          <div
            className="flex items-center space-x-2"
            onClick={(e) => e.stopPropagation()}
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.location.href = '/library'}
              title="Go to Library"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleBookmark}
              className={bookmarks.includes(currentChapter) ? 'text-yellow-500' : ''}
              title="Bookmark Chapter"
            >
              <Bookmark className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowHighlightMenu(!showHighlightMenu)}
              className="relative"
              title="Highlights"
            >
              <Highlighter className="h-4 w-4" />
              {highlights.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {highlights.length}
                </span>
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowCommentForm(!showCommentForm)}
              className="relative"
              title="Comments"
            >
              <MessageCircle className="h-4 w-4" />
              {marginComments.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {marginComments.length}
                </span>
              )}
            </Button>

            {/* Font Size Dropdown */}
            <div className="relative font-size-dropdown">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFontSizeMenu(!showFontSizeMenu)}
                title="Font Size"
              >
                <Type className="h-4 w-4" />
              </Button>
              {showFontSizeMenu && (
                <div className="absolute top-full right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 z-50">
                  <div className="flex flex-col space-y-2 min-w-[140px]">
                    <div className="text-xs font-medium text-center text-gray-600 dark:text-gray-400">Font Size</div>
                    <input
                      type="range"
                      min="12"
                      max="24"
                      step="2"
                      value={fontSize}
                      onChange={(e) => setFontSize(parseInt(e.target.value))}
                      className="w-full"
                    />
                    <div className="text-xs text-center font-medium">{fontSize}px</div>
                  </div>
                </div>
              )}
            </div>
            <Button variant="ghost" size="sm" onClick={() => setShowChapterList(!showChapterList)} title="Chapter List">
              <BookOpen className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)} title="Reader Settings">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden">
        <div
          ref={pageRef}
          className="h-full overflow-y-auto"
          style={{
            fontFamily: fontFamily === 'serif' ? 'Georgia, serif' : fontFamily === 'sans' ? 'Arial, sans-serif' : 'Courier, monospace',
            lineHeight: lineHeight
          }}
        >
          {isPdf ? (
            <div className="w-full h-full">
              <iframe
                src={pdfUrl}
                className="w-full h-full border-0"
                title={bookTitle}
              />
            </div>
          ) : (
            <div className="max-w-4xl mx-auto px-6 py-8">
              {/* Chapter Header */}
              <div className="mb-8">
                <h2
                  className="text-3xl font-bold mb-2"
                  style={{ fontSize: `${fontSize + 8}px` }}
                >
                  {currentChapterData?.title || `Chapter ${currentChapter + 1}`}
                </h2>
                <div className="text-sm opacity-70 mb-4">
                  Chapter {currentChapterData?.chapter_number || currentChapter + 1}
                  {currentChapterData?.word_count && ` • ${currentChapterData.word_count.toLocaleString()} words`}
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-6">
                  <div
                    className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${readingProgress}%` }}
                  />
                </div>
              </div>

              {/* Chapter Content */}
              <div
                ref={contentRef}
                className="prose prose-lg max-w-none"
                style={{ fontSize: `${fontSize}px` }}
                onMouseUp={handleTextSelection}
              >
                {currentChapterData?.content ? (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: renderTextWithHighlights(currentChapterData.content)
                    }}
                  />
                ) : (
                  <p className="text-gray-500 italic">No content available for this chapter.</p>
                )}
              </div>

              {/* Margin Comments */}
              {marginComments.length > 0 && (
                <div className="mt-12 border-t pt-8">
                  <h3 className="text-lg font-semibold mb-4">Comments</h3>
                  <div className="space-y-4">
                    {marginComments.map((comment) => (
                      <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                        <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          "{comment.position_data.anchorText}"
                        </div>
                        <div className="text-sm">{comment.content}</div>
                        <div className="text-xs text-gray-500 mt-2">
                          {new Date(comment.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Navigation Footer */}
      {!isPdf && chapters.length > 1 && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between max-w-4xl mx-auto">
            <Button
              variant="outline"
              onClick={prevChapter}
              disabled={currentChapter === 0 || isPageTurning}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>

            <div className="text-sm opacity-70 text-center">
              <div>{currentChapter + 1} of {chapters.length}</div>
              <div className="text-xs mt-1">{Math.round(readingProgress)}% complete</div>
            </div>

            <Button
              variant="outline"
              onClick={nextChapter}
              disabled={currentChapter === chapters.length - 1 || isPageTurning}
              className="flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Preview Mode Actions */}
      {isPreview && (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col gap-3">
          <Button
            onClick={handleClose}
            variant="outline"
            className="bg-white hover:bg-gray-50 text-gray-700 shadow-lg px-6 py-3 text-base font-semibold rounded-full border-2"
          >
            ← Back to Upload
          </Button>
          {onApprovePreview && (
            <Button
              onClick={onApprovePreview}
              className="bg-green-600 hover:bg-green-700 text-white shadow-lg px-6 py-3 text-base font-semibold rounded-full"
            >
              ✓ Approve & Publish Book
            </Button>
          )}
        </div>
      )}

      {/* Text Selection Menu */}
      {showHighlightMenu && selectedText && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Highlight Text</h3>
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg mb-4 text-sm">
                "{selectedText.text}"
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                {['#ffeb3b', '#4caf50', '#2196f3', '#ff9800', '#e91e63'].map((color) => (
                  <button
                    key={color}
                    className="w-8 h-8 rounded-full border-2 border-gray-300"
                    style={{ backgroundColor: color }}
                    onClick={() => createHighlight(color)}
                  />
                ))}
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowHighlightMenu(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowCommentForm(true)}>
                  Add Comment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Comment Form */}
      {showCommentForm && selectedText && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Add Comment</h3>
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg mb-4 text-sm">
                "{selectedText.text}"
              </div>
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Write your comment..."
                className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24 mb-4"
              />
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowCommentForm(false)}>
                  Cancel
                </Button>
                <Button onClick={createMarginComment} disabled={!newComment.trim()}>
                  Add Comment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Chapter List Modal */}
      {showChapterList && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-lg mx-4 max-h-[80vh] overflow-hidden">
            <CardContent className="p-0">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Chapters</h3>
                  <Button variant="ghost" size="sm" onClick={() => setShowChapterList(false)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {chapters.map((chapter, index) => (
                  <div
                    key={chapter.id}
                    className={`p-4 border-b cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
                      index === currentChapter ? 'bg-blue-50 dark:bg-blue-900' : ''
                    }`}
                    onClick={() => {
                      setCurrentChapter(index)
                      setShowChapterList(false)
                    }}
                  >
                    <div className="font-medium">{chapter.title}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Chapter {chapter.chapter_number} • {chapter.word_count?.toLocaleString()} words
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold">Reader Settings</h3>
                <Button variant="ghost" size="sm" onClick={() => setShowSettings(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-6">
                {/* Font Family */}
                <div>
                  <label className="block text-sm font-medium mb-2">Font Family</label>
                  <select
                    value={fontFamily}
                    onChange={(e) => setFontFamily(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                  >
                    <option value="serif">Serif</option>
                    <option value="sans">Sans Serif</option>
                    <option value="mono">Monospace</option>
                  </select>
                </div>

                {/* Font Size */}
                <div>
                  <label className="block text-sm font-medium mb-2">Font Size: {fontSize}px</label>
                  <input
                    type="range"
                    min="12"
                    max="24"
                    step="2"
                    value={fontSize}
                    onChange={(e) => setFontSize(parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>

                {/* Line Height */}
                <div>
                  <label className="block text-sm font-medium mb-2">Line Height: {lineHeight}</label>
                  <input
                    type="range"
                    min="1.2"
                    max="2.0"
                    step="0.1"
                    value={lineHeight}
                    onChange={(e) => setLineHeight(parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>

                {/* Theme */}
                <div>
                  <label className="block text-sm font-medium mb-2">Theme</label>
                  <div className="flex space-x-2">
                    <button
                      className={`px-3 py-2 rounded-lg border ${theme === 'light' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'}`}
                      onClick={() => setTheme('light')}
                    >
                      Light
                    </button>
                    <button
                      className={`px-3 py-2 rounded-lg border ${theme === 'dark' ? 'bg-blue-500 text-white' : 'bg-gray-800 text-white'}`}
                      onClick={() => setTheme('dark')}
                    >
                      Dark
                    </button>
                    <button
                      className={`px-3 py-2 rounded-lg border ${theme === 'sepia' ? 'bg-blue-500 text-white' : 'bg-yellow-50 text-yellow-900'}`}
                      onClick={() => setTheme('sepia')}
                    >
                      Sepia
                    </button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

export default EbookReader
